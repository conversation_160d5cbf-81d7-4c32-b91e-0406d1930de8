# Search Engine - C<PERSON>ng cụ tìm kiếm từ khóa trên website

## Mô tả
Đây là một chương trình Search Engine đơn giản được viết bằng Java, cho phép tìm kiếm từ khóa trên các trang web. Chương trình sẽ crawl các URL từ website ban đầu theo độ sâu cho trước, sau đó tìm kiếm từ khóa trong các trang web đó và lưu kết quả theo thứ tự số lần xuất hiện giảm dần.

## Tính năng chính
1. **Crawl website**: Thu thập danh sách các URL từ website ban đầu theo độ sâu tìm kiếm
2. **Tìm kiếm từ khóa**: Tìm kiếm từ khóa trong các URL đã crawl và đếm số lần xuất hiện
3. **Sắp xếp kết quả**: Sắ<PERSON> xếp các URL theo số lần xuất hiện từ khóa giảm dần
4. **Lưu kết quả**: Lưu kết quả tìm kiếm vào file text

## Cấu trúc chương trình
- **Main.java**: Lớp chính với menu điều hướng
- **WebCrawler.java**: Lớp thực hiện crawl website và thu thập URL
- **SearchEngine.java**: Lớp thực hiện tìm kiếm từ khóa và đếm số lần xuất hiện
- **URLResult.java**: Lớp lưu trữ thông tin URL và số lần xuất hiện từ khóa

## Cách sử dụng

### 1. Compile chương trình
```bash
javac *.java
```

### 2. Chạy chương trình
```bash
java Main
```

### 3. Sử dụng menu
Chương trình sẽ hiển thị menu với các lựa chọn:

1. **Nhập địa chỉ website và độ sâu tìm kiếm**
   - Nhập URL website bắt đầu (ví dụ: https://example.com)
   - Nhập độ sâu tìm kiếm (1-5, khuyến nghị: 2)

2. **Nhập từ khóa cần tìm kiếm**
   - Nhập từ khóa bạn muốn tìm kiếm

3. **Thực hiện crawl website**
   - Chương trình sẽ thu thập danh sách các URL từ website ban đầu
   - Quá trình này có thể mất vài phút tùy thuộc vào độ sâu và số lượng link

4. **Tìm kiếm từ khóa và lưu kết quả**
   - Tìm kiếm từ khóa trong các URL đã crawl
   - Hiển thị kết quả và lưu vào file

5. **Kết thúc chương trình**

## Ví dụ sử dụng

### Bước 1: Nhập thông tin website
```
Nhập địa chỉ website: https://vnexpress.net
Nhập độ sâu tìm kiếm: 2
```

### Bước 2: Nhập từ khóa
```
Nhập từ khóa cần tìm kiếm: công nghệ
```

### Bước 3: Crawl website
Chương trình sẽ thu thập các URL từ vnexpress.net với độ sâu 2

### Bước 4: Tìm kiếm và lưu kết quả
Chương trình sẽ tìm kiếm từ "công nghệ" trong các URL và lưu kết quả vào file

## Kết quả đầu ra
- Kết quả sẽ được hiển thị trên màn hình (top 10)
- File kết quả sẽ được lưu với tên `search_results_[timestamp].txt`
- File chứa danh sách URL được sắp xếp theo số lần xuất hiện từ khóa giảm dần

## Lưu ý kỹ thuật
- Chương trình sử dụng **HashMap** để lưu số lần xuất hiện từ khóa
- Sử dụng **TreeSet** với **Comparator** để sắp xếp kết quả
- Hỗ trợ tìm kiếm không phân biệt hoa thường
- Tự động loại bỏ HTML tags khi tìm kiếm
- Kiểm tra từ khóa là từ độc lập (không phải một phần của từ khác)

## Giới hạn
- Độ sâu crawl tối đa: 5 (để tránh crawl quá nhiều)
- Timeout kết nối: 5 giây
- Timeout đọc dữ liệu: 10 giây
- Chỉ crawl các file HTML (bỏ qua PDF, hình ảnh, CSS, JS...)

## Xử lý lỗi
- Tự động bỏ qua các URL không truy cập được
- Hiển thị thông báo lỗi chi tiết
- Tiếp tục xử lý các URL khác khi gặp lỗi

## Yêu cầu hệ thống
- Java 8 trở lên
- Kết nối Internet để crawl website
- Quyền ghi file để lưu kết quả

## Tác giả
Chương trình được phát triển dựa trên yêu cầu bài tập Search Engine với các tính năng:
- WebCrawler để thu thập URL
- HashMap để đếm từ khóa
- TreeMap và Comparator để sắp xếp kết quả
