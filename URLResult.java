/**
 * Lớp URLResult để lưu trữ thông tin URL và số lần xuất hiện từ khóa
 */
public class URLResult implements Comparable<URLResult> {
    private String url;
    private int keywordCount;
    
    /**
     * Constructor
     * @param url URL của trang web
     * @param keywordCount Số lần xuất hiện từ khóa
     */
    public URLResult(String url, int keywordCount) {
        this.url = url;
        this.keywordCount = keywordCount;
    }
    
    /**
     * Getter cho URL
     * @return URL
     */
    public String getUrl() {
        return url;
    }
    
    /**
     * Setter cho URL
     * @param url URL mới
     */
    public void setUrl(String url) {
        this.url = url;
    }
    
    /**
     * Getter cho số lần xuất hiện từ khóa
     * @return Số lần xuất hiện từ khóa
     */
    public int getKeywordCount() {
        return keywordCount;
    }
    
    /**
     * Setter cho số lần xuất hiện từ khóa
     * @param keywordCount Số lần xuất hiện từ khóa mới
     */
    public void setKeywordCount(int keywordCount) {
        this.keywordCount = keywordCount;
    }
    
    /**
     * Tăng số lần xuất hiện từ khóa lên 1
     */
    public void incrementKeywordCount() {
        this.keywordCount++;
    }
    
    /**
     * Tăng số lần xuất hiện từ khóa theo số lượng cho trước
     * @param count Số lượng cần tăng
     */
    public void addKeywordCount(int count) {
        this.keywordCount += count;
    }
    
    /**
     * So sánh hai URLResult theo số lần xuất hiện từ khóa (giảm dần)
     * Nếu số lần xuất hiện bằng nhau thì so sánh theo URL (tăng dần)
     * @param other URLResult khác để so sánh
     * @return Kết quả so sánh
     */
    @Override
    public int compareTo(URLResult other) {
        // So sánh theo keywordCount giảm dần
        int countComparison = Integer.compare(other.keywordCount, this.keywordCount);
        
        // Nếu keywordCount bằng nhau, so sánh theo URL tăng dần
        if (countComparison == 0) {
            return this.url.compareTo(other.url);
        }
        
        return countComparison;
    }
    
    /**
     * Kiểm tra hai URLResult có bằng nhau không (dựa trên URL)
     * @param obj Object khác để so sánh
     * @return true nếu bằng nhau
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        
        URLResult other = (URLResult) obj;
        return url != null ? url.equals(other.url) : other.url == null;
    }
    
    /**
     * Tính hash code dựa trên URL
     * @return Hash code
     */
    @Override
    public int hashCode() {
        return url != null ? url.hashCode() : 0;
    }
    
    /**
     * Chuyển đổi thành chuỗi để hiển thị
     * @return Chuỗi mô tả URLResult
     */
    @Override
    public String toString() {
        return String.format("URL: %s | Occurrences: %d", url, keywordCount);
    }
    
    /**
     * Chuyển đổi thành chuỗi để ghi vào file
     * @return Chuỗi định dạng để ghi file
     */
    public String toFileString() {
        return String.format("%s,%d", url, keywordCount);
    }
    
    /**
     * Tạo URLResult từ chuỗi đọc từ file
     * @param line Dòng đọc từ file (format: url,count)
     * @return URLResult hoặc null nếu format không đúng
     */
    public static URLResult fromFileString(String line) {
        try {
            String[] parts = line.split(",");
            if (parts.length >= 2) {
                String url = parts[0];
                int count = Integer.parseInt(parts[1]);
                return new URLResult(url, count);
            }
        } catch (NumberFormatException e) {
            System.out.println("Error parsing line: " + line);
        }
        return null;
    }
}
