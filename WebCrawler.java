import java.io.*;
import java.net.*;
import java.util.*;
import java.util.regex.*;

/**
 * WebCrawler class để thu thập các URL từ website ban đầu theo độ sâu tìm kiếm
 */
public class WebCrawler {
    private Set<String> visitedUrls;
    private Queue<String> urlQueue;
    private String baseUrl;
    private int maxDepth;
    private int currentDepth;
    
    public WebCrawler() {
        this.visitedUrls = new HashSet<>();
        this.urlQueue = new LinkedList<>();
    }
    
    /**
     * Bắt đầu crawl từ URL gốc với độ sâu cho trước
     * @param startUrl URL bắt đầu
     * @param depth Độ sâu tìm kiếm
     * @return Set chứa tất cả các URL đã crawl được
     */
    public Set<String> crawl(String startUrl, int depth) {
        this.baseUrl = getBaseUrl(startUrl);
        this.maxDepth = depth;
        this.currentDepth = 0;
        
        // Reset collections
        visitedUrls.clear();
        urlQueue.clear();
        
        // Thêm URL bắt đầu vào queue
        urlQueue.offer(startUrl);
        
        System.out.println("Bắt đầu crawl từ: " + startUrl);
        System.out.println("Độ sâu tối đa: " + depth);
        
        crawlLevel();
        
        System.out.println("Hoàn thành crawl. Tổng số URL tìm được: " + visitedUrls.size());
        return new HashSet<>(visitedUrls);
    }
    
    /**
     * Crawl theo từng level/độ sâu
     */
    private void crawlLevel() {
        while (currentDepth < maxDepth && !urlQueue.isEmpty()) {
            int levelSize = urlQueue.size();
            System.out.println("Đang crawl level " + (currentDepth + 1) + " với " + levelSize + " URLs");
            
            for (int i = 0; i < levelSize; i++) {
                String currentUrl = urlQueue.poll();
                if (currentUrl != null && !visitedUrls.contains(currentUrl)) {
                    visitedUrls.add(currentUrl);
                    
                    // Lấy các link từ trang hiện tại
                    Set<String> links = extractLinks(currentUrl);
                    
                    // Thêm các link mới vào queue cho level tiếp theo
                    for (String link : links) {
                        if (!visitedUrls.contains(link) && !urlQueue.contains(link)) {
                            urlQueue.offer(link);
                        }
                    }
                }
            }
            currentDepth++;
        }
    }
    
    /**
     * Trích xuất các link từ một URL
     * @param url URL cần trích xuất link
     * @return Set chứa các link tìm được
     */
    private Set<String> extractLinks(String url) {
        Set<String> links = new HashSet<>();
        
        try {
            System.out.println("Đang xử lý: " + url);
            
            // Tạo connection
            URL urlObj = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            
            // Kiểm tra response code
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                System.out.println("Lỗi khi truy cập " + url + ": " + responseCode);
                return links;
            }
            
            // Đọc nội dung HTML
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            StringBuilder content = new StringBuilder();
            String line;
            
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
            reader.close();
            
            // Trích xuất các link từ HTML
            links = parseLinks(content.toString(), url);
            
        } catch (Exception e) {
            System.out.println("Lỗi khi xử lý " + url + ": " + e.getMessage());
        }
        
        return links;
    }
    
    /**
     * Parse HTML để tìm các link
     * @param html Nội dung HTML
     * @param baseUrl URL gốc để resolve relative links
     * @return Set chứa các link tìm được
     */
    private Set<String> parseLinks(String html, String currentUrl) {
        Set<String> links = new HashSet<>();
        
        // Regex để tìm các thẻ <a href="...">
        Pattern pattern = Pattern.compile("<a\\s+[^>]*href\\s*=\\s*[\"']([^\"']+)[\"'][^>]*>", 
                                        Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(html);
        
        while (matcher.find()) {
            String link = matcher.group(1);
            String absoluteUrl = resolveUrl(link, currentUrl);
            
            if (absoluteUrl != null && isValidUrl(absoluteUrl)) {
                links.add(absoluteUrl);
            }
        }
        
        return links;
    }
    
    /**
     * Chuyển đổi relative URL thành absolute URL
     * @param link Link cần chuyển đổi
     * @param baseUrl URL gốc
     * @return Absolute URL hoặc null nếu không hợp lệ
     */
    private String resolveUrl(String link, String currentUrl) {
        try {
            if (link.startsWith("http://") || link.startsWith("https://")) {
                return link;
            }
            
            URL base = new URL(currentUrl);
            URL resolved = new URL(base, link);
            return resolved.toString();
            
        } catch (MalformedURLException e) {
            return null;
        }
    }
    
    /**
     * Kiểm tra URL có hợp lệ không
     * @param url URL cần kiểm tra
     * @return true nếu hợp lệ
     */
    private boolean isValidUrl(String url) {
        // Chỉ crawl các URL HTTP/HTTPS
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            return false;
        }
        
        // Bỏ qua các file không phải HTML
        String lowerUrl = url.toLowerCase();
        if (lowerUrl.contains(".pdf") || lowerUrl.contains(".jpg") || 
            lowerUrl.contains(".png") || lowerUrl.contains(".gif") ||
            lowerUrl.contains(".css") || lowerUrl.contains(".js") ||
            lowerUrl.contains(".zip") || lowerUrl.contains(".doc")) {
            return false;
        }
        
        // Bỏ qua các URL có fragment (#) hoặc query parameters phức tạp
        if (url.contains("#")) {
            url = url.substring(0, url.indexOf("#"));
        }
        
        return true;
    }
    
    /**
     * Lấy base URL từ một URL đầy đủ
     * @param url URL đầy đủ
     * @return Base URL
     */
    private String getBaseUrl(String url) {
        try {
            URL urlObj = new URL(url);
            return urlObj.getProtocol() + "://" + urlObj.getHost();
        } catch (MalformedURLException e) {
            return url;
        }
    }
    
    /**
     * Lấy danh sách các URL đã visit
     * @return Set chứa các URL đã visit
     */
    public Set<String> getVisitedUrls() {
        return new HashSet<>(visitedUrls);
    }
}
