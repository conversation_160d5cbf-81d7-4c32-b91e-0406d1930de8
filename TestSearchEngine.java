import java.util.*;

/**
 * Lớp test đơn giản để kiểm tra các thành phần của Search Engine
 */
public class TestSearchEngine {
    
    public static void main(String[] args) {
        System.out.println("=== TEST SEARCH ENGINE ===");
        
        // Test URLResult
        testURLResult();
        
        // Test WebCrawler với một website đơn giản
        testWebCrawler();
        
        // Test SearchEngine
        testSearchEngine();
        
        System.out.println("\n=== KẾT THÚC TEST ===");
    }
    
    /**
     * Test lớp URLResult
     */
    private static void testURLResult() {
        System.out.println("\n--- Test URLResult ---");
        
        // Tạo các URLResult
        URLResult result1 = new URLResult("https://example.com/page1", 5);
        URLResult result2 = new URLResult("https://example.com/page2", 3);
        URLResult result3 = new URLResult("https://example.com/page3", 5);
        
        // Test TreeSet với sắp xếp
        TreeSet<URLResult> results = new TreeSet<>();
        results.add(result1);
        results.add(result2);
        results.add(result3);
        
        System.out.println("Sorted results (by occurrence count descending):");
        for (URLResult result : results) {
            System.out.println(result.toString());
        }

        // Test other methods
        System.out.println("\nTest other methods:");
        System.out.println("result1.getUrl(): " + result1.getUrl());
        System.out.println("result1.getKeywordCount(): " + result1.getKeywordCount());

        result1.incrementKeywordCount();
        System.out.println("After increment: " + result1.getKeywordCount());

        result1.addKeywordCount(2);
        System.out.println("After add 2: " + result1.getKeywordCount());
    }
    
    /**
     * Test lớp WebCrawler với một website đơn giản
     */
    private static void testWebCrawler() {
        System.out.println("\n--- Test WebCrawler ---");
        
        WebCrawler crawler = new WebCrawler();
        
        // Test với một website đơn giản (có thể thay đổi URL này)
        String testUrl = "https://httpbin.org/html";
        int depth = 1;
        
        System.out.println("Testing crawl with URL: " + testUrl);
        System.out.println("Depth: " + depth);

        try {
            Set<String> urls = crawler.crawl(testUrl, depth);

            System.out.println("Crawl results:");
            System.out.println("Number of URLs: " + urls.size());

            if (!urls.isEmpty()) {
                System.out.println("URLs found:");
                int count = 0;
                for (String url : urls) {
                    if (count >= 3) {
                        System.out.println("... and " + (urls.size() - 3) + " other URLs");
                        break;
                    }
                    System.out.println("- " + url);
                    count++;
                }
            }

        } catch (Exception e) {
            System.out.println("Error testing WebCrawler: " + e.getMessage());
        }
    }
    
    /**
     * Test lớp SearchEngine
     */
    private static void testSearchEngine() {
        System.out.println("\n--- Test SearchEngine ---");
        
        SearchEngine searchEngine = new SearchEngine();
        
        // Tạo một set URL giả để test
        Set<String> testUrls = new HashSet<>();
        testUrls.add("https://httpbin.org/html");
        testUrls.add("https://example.com");
        
        String keyword = "html";
        
        System.out.println("Testing keyword search: \"" + keyword + "\"");
        System.out.println("In " + testUrls.size() + " test URLs");

        try {
            TreeSet<URLResult> results = searchEngine.search(testUrls, keyword);

            System.out.println("Search results:");
            if (results.isEmpty()) {
                System.out.println("No results found");
            } else {
                searchEngine.displayResults(results, 5);

                // Test save file
                String filename = "test_results.txt";
                boolean saved = searchEngine.saveResultsToFile(filename, results);
                if (saved) {
                    System.out.println("Test results saved to: " + filename);
                }
            }

        } catch (Exception e) {
            System.out.println("Error testing SearchEngine: " + e.getMessage());
        }
    }
}
