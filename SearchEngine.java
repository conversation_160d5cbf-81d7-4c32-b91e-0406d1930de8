import java.io.*;
import java.net.*;
import java.util.*;
import java.util.regex.*;

/**
 * SearchEngine class để tìm kiếm từ khóa trong các URL và đếm số lần xuất hiện
 */
public class SearchEngine {
    private HashMap<String, Integer> urlKeywordCount;
    private TreeSet<URLResult> sortedResults;
    private String keyword;
    
    public SearchEngine() {
        this.urlKeywordCount = new HashMap<>();
        this.sortedResults = new TreeSet<>();
    }
    
    /**
     * Tìm kiếm từ khóa trong danh sách các URL
     * @param urls Danh sách các URL cần tìm kiếm
     * @param keyword Từ khóa cần tìm
     * @return TreeSet chứa kết quả đã sắp xếp theo số lần xuất hiện giảm dần
     */
    public TreeSet<URLResult> search(Set<String> urls, String keyword) {
        this.keyword = keyword.toLowerCase().trim();
        
        // Reset collections
        urlKeywordCount.clear();
        sortedResults.clear();
        
        System.out.println("Starting keyword search: \"" + keyword + "\"");
        System.out.println("Number of URLs to check: " + urls.size());

        int processedCount = 0;
        int foundCount = 0;

        for (String url : urls) {
            processedCount++;
            System.out.println("Processing (" + processedCount + "/" + urls.size() + "): " + url);

            int count = searchKeywordInUrl(url);
            if (count > 0) {
                urlKeywordCount.put(url, count);
                sortedResults.add(new URLResult(url, count));
                foundCount++;
                System.out.println("  -> Found " + count + " occurrences");
            }
        }

        System.out.println("Search completed!");
        System.out.println("Total URLs containing keyword: " + foundCount + "/" + urls.size());
        
        return new TreeSet<>(sortedResults);
    }
    
    /**
     * Tìm kiếm từ khóa trong một URL cụ thể
     * @param url URL cần tìm kiếm
     * @return Số lần xuất hiện từ khóa
     */
    private int searchKeywordInUrl(String url) {
        try {
            // Tạo connection
            URL urlObj = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(10000);
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            
            // Check response code
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                System.out.println("  -> Access error: " + responseCode);
                return 0;
            }
            
            // Đọc nội dung
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            StringBuilder content = new StringBuilder();
            String line;
            
            while ((line = reader.readLine()) != null) {
                content.append(line).append(" ");
            }
            reader.close();
            
            // Đếm số lần xuất hiện từ khóa
            return countKeywordOccurrences(content.toString());
            
        } catch (Exception e) {
            System.out.println("  -> Error: " + e.getMessage());
            return 0;
        }
    }
    
    /**
     * Đếm số lần xuất hiện từ khóa trong nội dung
     * @param content Nội dung cần tìm kiếm
     * @return Số lần xuất hiện
     */
    private int countKeywordOccurrences(String content) {
        if (content == null || content.isEmpty() || keyword == null || keyword.isEmpty()) {
            return 0;
        }
        
        // Chuyển nội dung về lowercase để tìm kiếm không phân biệt hoa thường
        String lowerContent = content.toLowerCase();
        
        // Loại bỏ HTML tags để tìm kiếm chính xác hơn
        String cleanContent = removeHtmlTags(lowerContent);
        
        int count = 0;
        int index = 0;
        
        // Tìm kiếm từ khóa
        while ((index = cleanContent.indexOf(keyword, index)) != -1) {
            // Kiểm tra xem từ khóa có phải là từ độc lập không (không phải là một phần của từ khác)
            if (isWholeWord(cleanContent, index, keyword.length())) {
                count++;
            }
            index += keyword.length();
        }
        
        return count;
    }
    
    /**
     * Loại bỏ HTML tags khỏi nội dung
     * @param html Nội dung HTML
     * @return Nội dung đã loại bỏ HTML tags
     */
    private String removeHtmlTags(String html) {
        // Loại bỏ script và style tags cùng với nội dung
        html = html.replaceAll("(?i)<script[^>]*>.*?</script>", " ");
        html = html.replaceAll("(?i)<style[^>]*>.*?</style>", " ");
        
        // Loại bỏ tất cả HTML tags
        html = html.replaceAll("<[^>]+>", " ");
        
        // Loại bỏ HTML entities
        html = html.replaceAll("&[a-zA-Z0-9#]+;", " ");
        
        // Loại bỏ khoảng trắng thừa
        html = html.replaceAll("\\s+", " ");
        
        return html.trim();
    }
    
    /**
     * Kiểm tra xem từ khóa tìm được có phải là từ độc lập không
     * @param content Nội dung
     * @param startIndex Vị trí bắt đầu của từ khóa
     * @param keywordLength Độ dài từ khóa
     * @return true nếu là từ độc lập
     */
    private boolean isWholeWord(String content, int startIndex, int keywordLength) {
        // Kiểm tra ký tự trước từ khóa
        if (startIndex > 0) {
            char prevChar = content.charAt(startIndex - 1);
            if (Character.isLetterOrDigit(prevChar)) {
                return false;
            }
        }
        
        // Kiểm tra ký tự sau từ khóa
        int endIndex = startIndex + keywordLength;
        if (endIndex < content.length()) {
            char nextChar = content.charAt(endIndex);
            if (Character.isLetterOrDigit(nextChar)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Lưu kết quả tìm kiếm vào file
     * @param filename Tên file để lưu
     * @param results Kết quả tìm kiếm
     * @return true nếu lưu thành công
     */
    public boolean saveResultsToFile(String filename, TreeSet<URLResult> results) {
        try {
            FileWriter writer = new FileWriter(filename, false); // false = overwrite
            BufferedWriter bufferedWriter = new BufferedWriter(writer);
            
            // Write header
            bufferedWriter.write("=== SEARCH RESULTS ===\n");
            bufferedWriter.write("Keyword: " + keyword + "\n");
            bufferedWriter.write("Time: " + new Date().toString() + "\n");
            bufferedWriter.write("Total URLs found: " + results.size() + "\n");
            bufferedWriter.write("=====================================\n\n");
            
            // Ghi kết quả
            int rank = 1;
            for (URLResult result : results) {
                bufferedWriter.write(rank + ". " + result.toString() + "\n");
                rank++;
            }
            
            bufferedWriter.close();
            writer.close();
            
            System.out.println("Results saved to file: " + filename);
            return true;

        } catch (IOException e) {
            System.out.println("Error saving file: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Hiển thị kết quả tìm kiếm
     * @param results Kết quả tìm kiếm
     * @param maxResults Số lượng kết quả tối đa hiển thị (0 = hiển thị tất cả)
     */
    public void displayResults(TreeSet<URLResult> results, int maxResults) {
        if (results.isEmpty()) {
            System.out.println("No URLs found containing keyword \"" + keyword + "\"");
            return;
        }

        System.out.println("\n=== SEARCH RESULTS ===");
        System.out.println("Keyword: \"" + keyword + "\"");
        System.out.println("Total URLs found: " + results.size());
        System.out.println("========================");

        int count = 0;
        for (URLResult result : results) {
            if (maxResults > 0 && count >= maxResults) {
                System.out.println("... and " + (results.size() - maxResults) + " other results");
                break;
            }

            System.out.println((count + 1) + ". " + result.toString());
            count++;
        }
    }
    
    /**
     * Lấy từ khóa hiện tại
     * @return Từ khóa
     */
    public String getKeyword() {
        return keyword;
    }
    
    /**
     * Lấy HashMap chứa URL và số lần xuất hiện từ khóa
     * @return HashMap
     */
    public HashMap<String, Integer> getUrlKeywordCount() {
        return new HashMap<>(urlKeywordCount);
    }
}
