import java.util.*;
import java.io.*;

/**
 * Lớp Main với menu cho phép người dùng lựa chọn các chức năng của Search Engine
 */
public class Main {
    private static Scanner scanner = new Scanner(System.in);
    private static WebCrawler webCrawler = new WebCrawler();
    private static SearchEngine searchEngine = new SearchEngine();

    // Biến lưu trữ dữ liệu giữa các lần chạy
    private static String currentWebsite = "";
    private static int currentDepth = 1;
    private static Set<String> crawledUrls = new HashSet<>();
    private static String currentKeyword = "";
    private static TreeSet<URLResult> searchResults = new TreeSet<>();

    public static void main(String[] args) {
        System.out.println("=================================");
        System.out.println("    WELCOME TO SEARCH ENGINE");
        System.out.println("=================================");

        boolean running = true;
        while (running) {
            displayMenu();
            int choice = getUserChoice();

            switch (choice) {
                case 1:
                    inputWebsiteAndDepth();
                    break;
                case 2:
                    inputKeywordAndSearch();
                    break;
                case 3:
                    crawlWebsite();
                    break;
                case 4:
                    searchKeywordInCrawledUrls();
                    break;
                case 5:
                    running = false;
                    System.out.println("Thank you for using Search Engine!");
                    break;
                default:
                    System.out.println("Invalid choice. Please try again!");
            }

            if (running) {
                System.out.println("\nPress Enter to continue...");
                scanner.nextLine();
            }
        }

        scanner.close();
    }

    /**
     * Display main menu
     */
    private static void displayMenu() {
        System.out.println("\n=== MAIN MENU ===");
        System.out.println("1. Enter website URL and search depth");
        System.out.println("2. Enter keyword to search");
        System.out.println("3. Crawl website (collect URL list)");
        System.out.println("4. Search keyword and save results");
        System.out.println("5. Exit program");
        System.out.println("==================");

        // Display current information
        System.out.println("\nCurrent Information:");
        System.out.println("- Website: " + (currentWebsite.isEmpty() ? "Not entered" : currentWebsite));
        System.out.println("- Depth: " + currentDepth);
        System.out.println("- Crawled URLs: " + crawledUrls.size());
        System.out.println("- Keyword: " + (currentKeyword.isEmpty() ? "Not entered" : "\"" + currentKeyword + "\""));
        System.out.println("- Search results: " + searchResults.size() + " URLs");
    }

    /**
     * Get user choice
     * @return Choice number
     */
    private static int getUserChoice() {
        System.out.print("\nEnter your choice (1-5): ");
        try {
            int choice = Integer.parseInt(scanner.nextLine().trim());
            return choice;
        } catch (NumberFormatException e) {
            return -1;
        }
    }

    /**
     * Input website URL and search depth
     */
    private static void inputWebsiteAndDepth() {
        System.out.println("\n=== ENTER WEBSITE INFORMATION ===");

        // Input website
        System.out.print("Enter website URL (example: https://example.com): ");
        String website = scanner.nextLine().trim();

        if (website.isEmpty()) {
            System.out.println("Website URL cannot be empty!");
            return;
        }

        // Check and add http:// if needed
        if (!website.startsWith("http://") && !website.startsWith("https://")) {
            website = "http://" + website;
        }

        // Input depth
        System.out.print("Enter search depth (1-5, recommended: 2): ");
        try {
            int depth = Integer.parseInt(scanner.nextLine().trim());
            if (depth < 1 || depth > 5) {
                System.out.println("Depth must be between 1 and 5!");
                return;
            }

            currentWebsite = website;
            currentDepth = depth;

            // Reset old data when website changes
            crawledUrls.clear();
            searchResults.clear();
            currentKeyword = "";

            System.out.println("Information saved:");
            System.out.println("- Website: " + currentWebsite);
            System.out.println("- Depth: " + currentDepth);
            System.out.println("You can choose option 3 to start crawling the website.");

        } catch (NumberFormatException e) {
            System.out.println("Depth must be an integer!");
        }
    }

    /**
     * Input keyword to search
     */
    private static void inputKeywordAndSearch() {
        System.out.println("\n=== ENTER SEARCH KEYWORD ===");

        System.out.print("Enter keyword to search: ");
        String keyword = scanner.nextLine().trim();

        if (keyword.isEmpty()) {
            System.out.println("Keyword cannot be empty!");
            return;
        }

        currentKeyword = keyword;

        // Reset old search results
        searchResults.clear();

        System.out.println("Keyword saved: \"" + currentKeyword + "\"");

        if (crawledUrls.isEmpty()) {
            System.out.println("No URL list available for searching.");
            System.out.println("Please choose option 1 to enter website and option 3 to crawl first.");
        } else {
            System.out.println("You can choose option 4 to start searching in " + crawledUrls.size() + " crawled URLs.");
        }
    }

    /**
     * Crawl website
     */
    private static void crawlWebsite() {
        System.out.println("\n=== CRAWL WEBSITE ===");

        if (currentWebsite.isEmpty()) {
            System.out.println("Website URL not entered!");
            System.out.println("Please choose option 1 to enter website information first.");
            return;
        }

        System.out.println("Crawling website: " + currentWebsite);
        System.out.println("Depth: " + currentDepth);
        System.out.println("This process may take a few minutes...");

        try {
            crawledUrls = webCrawler.crawl(currentWebsite, currentDepth);

            System.out.println("\nCrawl completed!");
            System.out.println("Total URLs found: " + crawledUrls.size());

            // Display some sample URLs
            if (!crawledUrls.isEmpty()) {
                System.out.println("\nSome URLs found:");
                int count = 0;
                for (String url : crawledUrls) {
                    if (count >= 5) {
                        System.out.println("... and " + (crawledUrls.size() - 5) + " other URLs");
                        break;
                    }
                    System.out.println("- " + url);
                    count++;
                }
            }

            // Reset old search results
            searchResults.clear();

            if (!currentKeyword.isEmpty()) {
                System.out.println("\nYou can choose option 4 to search for keyword \"" + currentKeyword + "\" in these URLs.");
            } else {
                System.out.println("\nYou can choose option 2 to enter keyword and option 4 to search.");
            }

        } catch (Exception e) {
            System.out.println("Error while crawling website: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Search keyword in crawled URLs
     */
    private static void searchKeywordInCrawledUrls() {
        System.out.println("\n=== SEARCH KEYWORD ===");

        if (crawledUrls.isEmpty()) {
            System.out.println("No URL list available for searching!");
            System.out.println("Please choose option 1 to enter website and option 3 to crawl first.");
            return;
        }

        if (currentKeyword.isEmpty()) {
            System.out.println("Search keyword not entered!");
            System.out.println("Please choose option 2 to enter keyword first.");
            return;
        }

        System.out.println("Searching for keyword: \"" + currentKeyword + "\"");
        System.out.println("In " + crawledUrls.size() + " crawled URLs");
        System.out.println("This process may take a few minutes...");

        try {
            searchResults = searchEngine.search(crawledUrls, currentKeyword);

            System.out.println("\nSearch completed!");

            if (searchResults.isEmpty()) {
                System.out.println("No URLs found containing keyword \"" + currentKeyword + "\"");
                return;
            }

            // Display results
            searchEngine.displayResults(searchResults, 10); // Display top 10

            // Save results to file
            String filename = "search_results_" + System.currentTimeMillis() + ".txt";
            boolean saved = searchEngine.saveResultsToFile(filename, searchResults);

            if (saved) {
                System.out.println("\nResults saved to file: " + filename);
                System.out.println("File is sorted by keyword occurrence count in descending order.");
            }

        } catch (Exception e) {
            System.out.println("Error while searching: " + e.getMessage());
            e.printStackTrace();
        }
    }
}