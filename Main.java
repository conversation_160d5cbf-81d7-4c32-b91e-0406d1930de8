import java.util.*;
import java.io.*;

/**
 * Lớp Main với menu cho phép người dùng lựa chọn các chức năng của Search Engine
 */
public class Main {
    private static Scanner scanner = new Scanner(System.in);
    private static WebCrawler webCrawler = new WebCrawler();
    private static SearchEngine searchEngine = new SearchEngine();

    // Biến lưu trữ dữ liệu giữa các lần chạy
    private static String currentWebsite = "";
    private static int currentDepth = 1;
    private static Set<String> crawledUrls = new HashSet<>();
    private static String currentKeyword = "";
    private static TreeSet<URLResult> searchResults = new TreeSet<>();

    public static void main(String[] args) {
        System.out.println("=================================");
        System.out.println("    CHÀO MỪNG ĐẾN SEARCH ENGINE");
        System.out.println("=================================");

        boolean running = true;
        while (running) {
            displayMenu();
            int choice = getUserChoice();

            switch (choice) {
                case 1:
                    inputWebsiteAndDepth();
                    break;
                case 2:
                    inputKeywordAndSearch();
                    break;
                case 3:
                    crawlWebsite();
                    break;
                case 4:
                    searchKeywordInCrawledUrls();
                    break;
                case 5:
                    running = false;
                    System.out.println("Cảm ơn bạn đã sử dụng Search Engine!");
                    break;
                default:
                    System.out.println("Lựa chọn không hợp lệ. Vui lòng chọn lại!");
            }

            if (running) {
                System.out.println("\nNhấn Enter để tiếp tục...");
                scanner.nextLine();
            }
        }

        scanner.close();
    }

    /**
     * Hiển thị menu chính
     */
    private static void displayMenu() {
        System.out.println("\n=== MENU CHÍNH ===");
        System.out.println("1. Nhập địa chỉ website và độ sâu tìm kiếm");
        System.out.println("2. Nhập từ khóa cần tìm kiếm");
        System.out.println("3. Thực hiện crawl website (lập danh sách các đường link)");
        System.out.println("4. Tìm kiếm từ khóa và lưu kết quả");
        System.out.println("5. Kết thúc chương trình");
        System.out.println("==================");

        // Hiển thị thông tin hiện tại
        System.out.println("\nThông tin hiện tại:");
        System.out.println("- Website: " + (currentWebsite.isEmpty() ? "Chưa nhập" : currentWebsite));
        System.out.println("- Độ sâu: " + currentDepth);
        System.out.println("- Số URL đã crawl: " + crawledUrls.size());
        System.out.println("- Từ khóa: " + (currentKeyword.isEmpty() ? "Chưa nhập" : "\"" + currentKeyword + "\""));
        System.out.println("- Kết quả tìm kiếm: " + searchResults.size() + " URL");
    }

    /**
     * Lấy lựa chọn từ người dùng
     * @return Số lựa chọn
     */
    private static int getUserChoice() {
        System.out.print("\nNhập lựa chọn của bạn (1-5): ");
        try {
            int choice = Integer.parseInt(scanner.nextLine().trim());
            return choice;
        } catch (NumberFormatException e) {
            return -1;
        }
    }

    /**
     * Nhập địa chỉ website và độ sâu tìm kiếm
     */
    private static void inputWebsiteAndDepth() {
        System.out.println("\n=== NHẬP THÔNG TIN WEBSITE ===");

        // Nhập website
        System.out.print("Nhập địa chỉ website (ví dụ: https://example.com): ");
        String website = scanner.nextLine().trim();

        if (website.isEmpty()) {
            System.out.println("Địa chỉ website không được để trống!");
            return;
        }

        // Kiểm tra và thêm http:// nếu cần
        if (!website.startsWith("http://") && !website.startsWith("https://")) {
            website = "http://" + website;
        }

        // Nhập độ sâu
        System.out.print("Nhập độ sâu tìm kiếm (1-5, khuyến nghị: 2): ");
        try {
            int depth = Integer.parseInt(scanner.nextLine().trim());
            if (depth < 1 || depth > 5) {
                System.out.println("Độ sâu phải từ 1 đến 5!");
                return;
            }

            currentWebsite = website;
            currentDepth = depth;

            // Reset dữ liệu cũ nếu website thay đổi
            crawledUrls.clear();
            searchResults.clear();
            currentKeyword = "";

            System.out.println("Đã lưu thông tin:");
            System.out.println("- Website: " + currentWebsite);
            System.out.println("- Độ sâu: " + currentDepth);
            System.out.println("Bạn có thể chọn mục 3 để bắt đầu crawl website.");

        } catch (NumberFormatException e) {
            System.out.println("Độ sâu phải là một số nguyên!");
        }
    }

    /**
     * Nhập từ khóa cần tìm kiếm
     */
    private static void inputKeywordAndSearch() {
        System.out.println("\n=== NHẬP TỪ KHÓA TÌM KIẾM ===");

        System.out.print("Nhập từ khóa cần tìm kiếm: ");
        String keyword = scanner.nextLine().trim();

        if (keyword.isEmpty()) {
            System.out.println("Từ khóa không được để trống!");
            return;
        }

        currentKeyword = keyword;

        // Reset kết quả tìm kiếm cũ
        searchResults.clear();

        System.out.println("Đã lưu từ khóa: \"" + currentKeyword + "\"");

        if (crawledUrls.isEmpty()) {
            System.out.println("Chưa có danh sách URL để tìm kiếm.");
            System.out.println("Vui lòng chọn mục 1 để nhập website và mục 3 để crawl trước.");
        } else {
            System.out.println("Bạn có thể chọn mục 4 để bắt đầu tìm kiếm trong " + crawledUrls.size() + " URL đã crawl.");
        }
    }

    /**
     * Thực hiện crawl website
     */
    private static void crawlWebsite() {
        System.out.println("\n=== CRAWL WEBSITE ===");

        if (currentWebsite.isEmpty()) {
            System.out.println("Chưa nhập địa chỉ website!");
            System.out.println("Vui lòng chọn mục 1 để nhập thông tin website trước.");
            return;
        }

        System.out.println("Đang crawl website: " + currentWebsite);
        System.out.println("Độ sâu: " + currentDepth);
        System.out.println("Quá trình này có thể mất vài phút...");

        try {
            crawledUrls = webCrawler.crawl(currentWebsite, currentDepth);

            System.out.println("\nCrawl hoàn thành!");
            System.out.println("Tổng số URL tìm được: " + crawledUrls.size());

            // Hiển thị một số URL mẫu
            if (!crawledUrls.isEmpty()) {
                System.out.println("\nMột số URL tìm được:");
                int count = 0;
                for (String url : crawledUrls) {
                    if (count >= 5) {
                        System.out.println("... và " + (crawledUrls.size() - 5) + " URL khác");
                        break;
                    }
                    System.out.println("- " + url);
                    count++;
                }
            }

            // Reset kết quả tìm kiếm cũ
            searchResults.clear();

            if (!currentKeyword.isEmpty()) {
                System.out.println("\nBạn có thể chọn mục 4 để tìm kiếm từ khóa \"" + currentKeyword + "\" trong các URL này.");
            } else {
                System.out.println("\nBạn có thể chọn mục 2 để nhập từ khóa và mục 4 để tìm kiếm.");
            }

        } catch (Exception e) {
            System.out.println("Lỗi khi crawl website: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Tìm kiếm từ khóa trong các URL đã crawl
     */
    private static void searchKeywordInCrawledUrls() {
        System.out.println("\n=== TÌM KIẾM TỪ KHÓA ===");

        if (crawledUrls.isEmpty()) {
            System.out.println("Chưa có danh sách URL để tìm kiếm!");
            System.out.println("Vui lòng chọn mục 1 để nhập website và mục 3 để crawl trước.");
            return;
        }

        if (currentKeyword.isEmpty()) {
            System.out.println("Chưa nhập từ khóa tìm kiếm!");
            System.out.println("Vui lòng chọn mục 2 để nhập từ khóa trước.");
            return;
        }

        System.out.println("Đang tìm kiếm từ khóa: \"" + currentKeyword + "\"");
        System.out.println("Trong " + crawledUrls.size() + " URL đã crawl");
        System.out.println("Quá trình này có thể mất vài phút...");

        try {
            searchResults = searchEngine.search(crawledUrls, currentKeyword);

            System.out.println("\nTìm kiếm hoàn thành!");

            if (searchResults.isEmpty()) {
                System.out.println("Không tìm thấy URL nào chứa từ khóa \"" + currentKeyword + "\"");
                return;
            }

            // Hiển thị kết quả
            searchEngine.displayResults(searchResults, 10); // Hiển thị top 10

            // Lưu kết quả vào file
            String filename = "search_results_" + System.currentTimeMillis() + ".txt";
            boolean saved = searchEngine.saveResultsToFile(filename, searchResults);

            if (saved) {
                System.out.println("\nKết quả đã được lưu vào file: " + filename);
                System.out.println("File được sắp xếp theo số lần xuất hiện từ khóa giảm dần.");
            }

        } catch (Exception e) {
            System.out.println("Lỗi khi tìm kiếm: " + e.getMessage());
            e.printStackTrace();
        }
    }
}